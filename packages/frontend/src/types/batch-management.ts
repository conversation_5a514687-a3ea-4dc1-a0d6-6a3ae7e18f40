// Batch Management Types
export interface BatchManagementQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: BatchStatus;
  complianceStatus?: ComplianceStatus;
  productId?: string;
  supplierId?: string;
  expiryDateFrom?: string;
  expiryDateTo?: string;
  batchNumber?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface BatchManagementStats {
  totalBatches: number;
  activeBatches: number;
  expiringSoon: number;
  expired: number;
  validationErrors: number;
  bpomCompliant: number;
  recentActivity: number;
  complianceRate: number;
  validationSuccessRate: number;
}

export interface BatchHistoryItem {
  id: string;
  batchNumber: string;
  productId: string;
  productName: string;
  supplierId?: string;
  supplierName?: string;
  status: BatchStatus;
  complianceStatus: ComplianceStatus;
  expiryDate?: Date | string;
  manufacturingDate?: Date | string;
  quantityOnHand: number;
  quantityAllocated: number;
  location?: string;
  receivedDate: Date | string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface BatchValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  validationLevel: ValidationLevel;
  bpomCompliant: boolean;
  uniquenessCheck: {
    isUnique: boolean;
    conflictingBatches?: ConflictingBatch[];
  };
  formatValidation: {
    passedRules: string[];
    failedRules: string[];
    recommendedFormat?: string;
  };
  auditTrail?: {
    validationId: string;
    timestamp: Date | string;
    userId: string;
  };
}

export interface ConflictingBatch {
  id: string;
  batchNumber: string;
  productName: string;
  supplierName: string;
  createdAt: Date | string;
}

export interface BPOMComplianceData {
  overallCompliance: number;
  bpomCompliant: number;
  controlledSubstances: number;
  pendingReview: number;
  nonCompliant: number;
  recentAudits: number;
  nextAuditDue?: string;
  complianceCategories: ComplianceCategory[];
  recentIssues: ComplianceIssue[];
}

export interface ComplianceCategory {
  name: string;
  compliant: number;
  total: number;
  level: 'high' | 'medium' | 'low';
}

export interface ComplianceIssue {
  batchNumber: string;
  issue: string;
  severity: 'high' | 'medium' | 'low';
  date: string;
  productName?: string;
}

export interface BatchAuditLogItem {
  id: string;
  batchNumber: string;
  action: BatchAuditAction;
  status: 'SUCCESS' | 'FAILED' | 'WARNING';
  productId?: string;
  productName?: string;
  supplierId?: string;
  supplierName?: string;
  userId?: string;
  userName?: string;
  referenceType?: string;
  referenceId?: string;
  referenceNumber?: string;
  validationRules?: string[];
  validationResults?: any;
  bpomCompliant?: boolean;
  complianceNotes?: string;
  complianceLevel?: string;
  message?: string;
  details?: string;
  errorMessage?: string;
  createdAt: Date | string;
}

export interface ExpiryTrackingItem {
  id: string;
  batchNumber: string;
  productId: string;
  productName: string;
  supplierId?: string;
  supplierName?: string;
  expiryDate: Date | string;
  daysUntilExpiry: number;
  quantityOnHand: number;
  location?: string;
  status: 'ACTIVE' | 'EXPIRING_SOON' | 'EXPIRED';
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

// API Response Types
export interface BatchManagementResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Enums
export type BatchStatus = 
  | 'ACTIVE'
  | 'EXPIRED'
  | 'EXPIRING_SOON'
  | 'RECALLED'
  | 'QUARANTINE'
  | 'DISPOSED';

export type ComplianceStatus = 
  | 'COMPLIANT'
  | 'NON_COMPLIANT'
  | 'PENDING_REVIEW'
  | 'CONTROLLED'
  | 'IMPORTED';

export type ValidationLevel = 
  | 'BASIC'
  | 'STANDARD'
  | 'BPOM_COMPLIANT'
  | 'CONTROLLED';

export type BatchAuditAction = 
  | 'CREATED'
  | 'UPDATED'
  | 'VALIDATED'
  | 'REJECTED'
  | 'SUBSTITUTED'
  | 'EXPIRED'
  | 'RECALLED'
  | 'COMPLIANCE_CHECK'
  | 'FORMAT_VALIDATION'
  | 'UNIQUENESS_CHECK'
  | 'GOODS_RECEIPT'
  | 'INVENTORY_CREATED'
  | 'STOCK_MOVEMENT';

// Form DTOs
export interface ValidateBatchNumberDto {
  batchNumber: string;
  productId: string;
  supplierId?: string;
  expiryDate?: string;
  manufacturingDate?: string;
  isSubstitution?: boolean;
  originalBatchNumber?: string;
}

export interface BPOMComplianceCheckDto {
  productId: string;
  batchNumber: string;
  userId?: string;
}

export interface BatchExportParams {
  format: 'CSV' | 'EXCEL' | 'PDF';
  filters?: BatchManagementQueryParams;
  includeAuditTrail?: boolean;
  includeComplianceData?: boolean;
}
