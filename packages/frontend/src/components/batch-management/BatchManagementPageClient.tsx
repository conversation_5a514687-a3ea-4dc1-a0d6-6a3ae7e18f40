"use client";

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Download, RefreshCw, X } from "lucide-react";
import { BatchValidationStats } from "./BatchValidationStats";
import { ComplianceReportCard } from "./ComplianceReportCard";
import { BatchHistoryTable } from "./BatchHistoryTable";
import { ExpiryTrackingTable } from "./ExpiryTrackingTable";
import { ComplianceTable } from "./ComplianceTable";
import { AuditLogTable } from "./AuditLogTable";
import { BatchManagementQueryParams } from "@/types/batch-management";
import {
  BATCH_STATUS_OPTIONS,
  COMPLIANCE_STATUS_OPTIONS,
} from "@/lib/constants/batch-management";
import {
  useBatchManagementInvalidate,
  useExportBatchData,
} from "@/hooks/useBatchManagement";
import { toast } from "sonner";

interface BatchManagementPageClientProps {
  initialQuery: BatchManagementQueryParams;
}

export function BatchManagementPageClient({
  initialQuery,
}: BatchManagementPageClientProps) {
  const router = useRouter();

  // Query state - this will trigger the TanStack Query
  const [query, setQuery] = useState<BatchManagementQueryParams>(initialQuery);
  const [activeTab, setActiveTab] = useState("overview");

  // Use invalidation hook for data refresh
  const { invalidateAll } = useBatchManagementInvalidate();
  const exportBatchDataMutation = useExportBatchData();

  // Update query and URL
  const updateQuery = useCallback(
    (updates: Partial<BatchManagementQueryParams>) => {
      const newQuery = { ...query, ...updates, page: 1 }; // Reset to page 1 when filters change
      setQuery(newQuery);

      // Update URL with new query parameters
      const params = new URLSearchParams();
      Object.entries(newQuery).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.set(key, value.toString());
        }
      });

      const newUrl = `${window.location.pathname}?${params.toString()}`;
      router.push(newUrl, { scroll: false });
    },
    [query, router],
  );

  const handleExport = async () => {
    try {
      await exportBatchDataMutation.mutateAsync({
        format: "EXCEL",
        filters: query,
        includeAuditTrail: true,
        includeComplianceData: true,
      });
      toast.success("Data batch berhasil diekspor");
    } catch (error) {
      toast.error("Gagal mengekspor data batch");
    }
  };

  const handleRefresh = async () => {
    try {
      await invalidateAll();
      toast.success("Data berhasil diperbarui");
    } catch (error) {
      toast.error("Gagal memperbarui data");
    }
  };

  const clearFilters = () => {
    const clearedQuery = {
      page: 1,
      limit: query.limit,
      sortBy: query.sortBy,
      sortOrder: query.sortOrder,
    };
    setQuery(clearedQuery);
    router.push(window.location.pathname, { scroll: false });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Manajemen Batch Number
          </h1>
          <p className="text-muted-foreground">
            Dashboard untuk mengelola batch number, validasi, dan compliance
            BPOM
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={exportBatchDataMutation.isPending}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={handleExport}
            disabled={exportBatchDataMutation.isPending}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-lg">Filter & Pencarian</CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-2" />
              Bersihkan Filter
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Pencarian</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari batch number, produk..."
                  value={query.search || ""}
                  onChange={(e) => updateQuery({ search: e.target.value })}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select
                value={query.status || ""}
                onValueChange={(value) =>
                  updateQuery({ status: (value as any) || undefined })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih status..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  {BATCH_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Compliance</label>
              <Select
                value={query.complianceStatus || ""}
                onValueChange={(value) =>
                  updateQuery({ complianceStatus: (value as any) || undefined })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih compliance..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua</SelectItem>
                  {COMPLIANCE_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Batch Number</label>
              <Input
                placeholder="Nomor batch spesifik..."
                value={query.batchNumber || ""}
                onChange={(e) => updateQuery({ batchNumber: e.target.value })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Ringkasan</TabsTrigger>
          <TabsTrigger value="history">Riwayat Batch</TabsTrigger>
          <TabsTrigger value="validation">Validasi</TabsTrigger>
          <TabsTrigger value="expiry">Tracking Kedaluwarsa</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="audit">Audit Log</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <BatchValidationStats />
            <ComplianceReportCard />
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <BatchHistoryTable
            query={query}
            onQueryChange={updateQuery}
            onView={(batch) => console.log("View batch:", batch)}
            onEdit={(batch) => console.log("Edit batch:", batch)}
            onDelete={(batch) => console.log("Delete batch:", batch)}
          />
        </TabsContent>

        <TabsContent value="validation" className="space-y-4">
          <BatchValidationStats detailed />
        </TabsContent>

        <TabsContent value="expiry" className="space-y-4">
          <ExpiryTrackingTable
            query={query}
            onQueryChange={updateQuery}
            onView={(item) => console.log("View expiry item:", item)}
            onAction={(item, action) =>
              console.log("Action on expiry item:", item, action)
            }
          />
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <div className="space-y-4">
            <ComplianceReportCard detailed />
            <ComplianceTable
              query={query}
              onQueryChange={updateQuery}
              onView={(batch) => console.log("View compliance batch:", batch)}
              onComplianceCheck={(batch) =>
                console.log("Compliance check:", batch)
              }
              onGenerateReport={(batch) =>
                console.log("Generate report:", batch)
              }
            />
          </div>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <AuditLogTable
            query={query}
            onQueryChange={updateQuery}
            onView={(log) => console.log("View audit log:", log)}
            onViewDetails={(log) => console.log("View audit details:", log)}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
