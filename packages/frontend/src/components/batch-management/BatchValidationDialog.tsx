'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ProductSelector } from '@/components/ui/product-selector';
import { SupplierSelector } from '@/components/ui/supplier-selector';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  <PERSON>,
  FileCheck
} from 'lucide-react';
import { useValidateBatchNumber } from '@/hooks/useBatchManagement';
import { BatchValidationResult } from '@/types/batch-management';

const batchValidationSchema = z.object({
  batchNumber: z.string().min(1, 'Batch number harus diisi'),
  productId: z.string().min(1, 'Produk harus dipilih'),
  supplierId: z.string().optional(),
  expiryDate: z.string().optional(),
  manufacturingDate: z.string().optional(),
  isSubstitution: z.boolean().optional(),
  originalBatchNumber: z.string().optional(),
});

type BatchValidationFormData = z.infer<typeof batchValidationSchema>;

interface BatchValidationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialData?: Partial<BatchValidationFormData>;
}

export function BatchValidationDialog({
  open,
  onOpenChange,
  initialData,
}: BatchValidationDialogProps) {
  const [validationResult, setValidationResult] = useState<BatchValidationResult | null>(null);
  const validateBatchMutation = useValidateBatchNumber();

  const form = useForm<BatchValidationFormData>({
    resolver: zodResolver(batchValidationSchema),
    defaultValues: {
      batchNumber: '',
      productId: '',
      supplierId: undefined,
      expiryDate: undefined,
      manufacturingDate: undefined,
      isSubstitution: undefined,
      originalBatchNumber: undefined,
      ...initialData,
    },
  });

  const onSubmit = async (data: BatchValidationFormData) => {
    try {
      const result = await validateBatchMutation.mutateAsync(data);
      setValidationResult(result);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleClose = () => {
    form.reset();
    setValidationResult(null);
    onOpenChange(false);
  };

  const getValidationStatusBadge = (isValid: boolean) => {
    return isValid ? (
      <Badge variant="default" className="gap-1">
        <CheckCircle className="h-3 w-3" />
        Valid
      </Badge>
    ) : (
      <Badge variant="destructive" className="gap-1">
        <XCircle className="h-3 w-3" />
        Tidak Valid
      </Badge>
    );
  };

  const getBPOMComplianceBadge = (compliant: boolean) => {
    return compliant ? (
      <Badge variant="default" className="gap-1">
        <Shield className="h-3 w-3" />
        BPOM Compliant
      </Badge>
    ) : (
      <Badge variant="destructive" className="gap-1">
        <AlertTriangle className="h-3 w-3" />
        Non-Compliant
      </Badge>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileCheck className="h-5 w-5" />
            Validasi Batch Number
          </DialogTitle>
          <DialogDescription>
            Lakukan validasi batch number untuk memastikan kepatuhan terhadap aturan BPOM
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form Section */}
          <div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="batchNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Batch Number *</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan batch number..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="productId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Produk *</FormLabel>
                      <FormControl>
                        <ProductSelector
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Pilih produk..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="supplierId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Supplier</FormLabel>
                      <FormControl>
                        <SupplierSelector
                          value={field.value || ''}
                          onValueChange={field.onChange}
                          placeholder="Pilih supplier..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="manufacturingDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tanggal Produksi</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expiryDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tanggal Kedaluwarsa</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={validateBatchMutation.isPending}
                >
                  {validateBatchMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Validasi Batch Number
                </Button>
              </form>
            </Form>
          </div>

          {/* Results Section */}
          <div>
            {validationResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Hasil Validasi
                    {getValidationStatusBadge(validationResult.isValid)}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* BPOM Compliance */}
                  <div className="flex items-center justify-between">
                    <span className="font-medium">BPOM Compliance:</span>
                    {getBPOMComplianceBadge(validationResult.bpomCompliant)}
                  </div>

                  {/* Validation Level */}
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Level Validasi:</span>
                    <Badge variant="outline">{validationResult.validationLevel}</Badge>
                  </div>

                  <Separator />

                  {/* Uniqueness Check */}
                  <div>
                    <h4 className="font-medium mb-2">Pemeriksaan Keunikan:</h4>
                    <div className="flex items-center gap-2">
                      {validationResult.uniquenessCheck.isUnique ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <span className="text-sm">
                        {validationResult.uniquenessCheck.isUnique
                          ? 'Batch number unik'
                          : 'Batch number sudah ada'}
                      </span>
                    </div>
                  </div>

                  {/* Format Validation */}
                  <div>
                    <h4 className="font-medium mb-2">Validasi Format:</h4>
                    <div className="space-y-1">
                      {validationResult.formatValidation.passedRules.map((rule, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span>{rule}</span>
                        </div>
                      ))}
                      {validationResult.formatValidation.failedRules.map((rule, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <XCircle className="h-3 w-3 text-red-500" />
                          <span>{rule}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Errors */}
                  {validationResult.errors.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2 text-red-600">Error:</h4>
                      <ul className="space-y-1">
                        {validationResult.errors.map((error, index) => (
                          <li key={index} className="text-sm text-red-600 flex items-start gap-2">
                            <XCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                            {error}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Warnings */}
                  {validationResult.warnings.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2 text-yellow-600">Peringatan:</h4>
                      <ul className="space-y-1">
                        {validationResult.warnings.map((warning, index) => (
                          <li key={index} className="text-sm text-yellow-600 flex items-start gap-2">
                            <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                            {warning}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Tutup
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
