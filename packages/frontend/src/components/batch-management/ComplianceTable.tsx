'use client';

import React from 'react';
import {
  ColumnDef,
  flexRender,
  useReactTable,
  getCoreRowModel,
  SortingState,
  VisibilityState
} from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { Shield, AlertTriangle, CheckCircle, Clock, Eye, FileText } from 'lucide-react';
import { BatchHistoryItem, BatchManagementQueryParams } from '@/types/batch-management';
import { useBatchComplianceStatus } from '@/hooks/useBatchManagement';
import { formatDate } from '@/lib/utils';
import { COMPLIANCE_STATUS_OPTIONS } from '@/lib/constants/batch-management';

interface ComplianceTableProps {
  query: BatchManagementQueryParams;
  onQueryChange: (query: BatchManagementQueryParams) => void;
  onView?: (batch: BatchHistoryItem) => void;
  onComplianceCheck?: (batch: BatchHistoryItem) => void;
  onGenerateReport?: (batch: BatchHistoryItem) => void;
}

export function ComplianceTable({
  query,
  onQueryChange,
  onView,
  onComplianceCheck,
  onGenerateReport,
}: ComplianceTableProps) {
  // Transform query for compliance API
  const complianceParams = {
    productIds: query.productId ? [query.productId] : undefined,
    batchNumbers: query.batchNumber ? [query.batchNumber] : undefined,
    limit: query.limit || 10,
    offset: ((query.page || 1) - 1) * (query.limit || 10),
  };

  const { data, isLoading, error } = useBatchComplianceStatus(complianceParams);

  const getComplianceBadge = (status: string) => {
    const option = COMPLIANCE_STATUS_OPTIONS.find(opt => opt.value === status);
    if (!option) return <Badge variant="secondary">{status}</Badge>;

    const variants: Record<string, any> = {
      COMPLIANT: 'default',
      NON_COMPLIANT: 'destructive',
      PENDING_REVIEW: 'secondary',
      CONTROLLED: 'outline',
      IMPORTED: 'outline',
    };

    const icons: Record<string, any> = {
      COMPLIANT: <CheckCircle className="h-3 w-3" />,
      NON_COMPLIANT: <AlertTriangle className="h-3 w-3" />,
      PENDING_REVIEW: <Clock className="h-3 w-3" />,
      CONTROLLED: <Shield className="h-3 w-3" />,
      IMPORTED: <Shield className="h-3 w-3" />,
    };

    return (
      <Badge variant={variants[status] || 'secondary'} className="gap-1">
        {icons[status]}
        {option.label}
      </Badge>
    );
  };

  const getComplianceLevel = (status: string) => {
    switch (status) {
      case 'COMPLIANT':
        return { level: 'Tinggi', color: 'text-green-600' };
      case 'NON_COMPLIANT':
        return { level: 'Rendah', color: 'text-red-600' };
      case 'PENDING_REVIEW':
        return { level: 'Sedang', color: 'text-yellow-600' };
      case 'CONTROLLED':
        return { level: 'Terkontrol', color: 'text-blue-600' };
      case 'IMPORTED':
        return { level: 'Import', color: 'text-purple-600' };
      default:
        return { level: 'Tidak Diketahui', color: 'text-gray-600' };
    }
  };

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'batchNumber',
      header: 'Batch Number',
      cell: ({ row }) => (
        <div className="font-medium">
          {row.getValue('batchNumber')}
        </div>
      ),
    },
    {
      accessorKey: 'productName',
      header: 'Produk',
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate">
          {row.getValue('productName')}
        </div>
      ),
    },
    {
      accessorKey: 'supplierName',
      header: 'Supplier',
      cell: ({ row }) => (
        <div className="max-w-[150px] truncate">
          {row.getValue('supplierName') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'complianceStatus',
      header: 'Status Compliance',
      cell: ({ row }) => getComplianceBadge(row.getValue('complianceStatus')),
    },
    {
      accessorKey: 'complianceLevel',
      header: 'Level Compliance',
      cell: ({ row }) => {
        const status = row.getValue('complianceStatus') as string;
        const { level, color } = getComplianceLevel(status);
        return (
          <span className={`font-medium ${color}`}>
            {level}
          </span>
        );
      },
    },
    {
      accessorKey: 'lastComplianceCheck',
      header: 'Pemeriksaan Terakhir',
      cell: ({ row }) => {
        const date = row.getValue('lastComplianceCheck') as string;
        return date ? formatDate(date) : '-';
      },
    },
    {
      accessorKey: 'nextAuditDue',
      header: 'Audit Berikutnya',
      cell: ({ row }) => {
        const date = row.getValue('nextAuditDue') as string;
        if (!date) return '-';

        const auditDate = new Date(date);
        const now = new Date();
        const daysUntilAudit = Math.ceil((auditDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

        return (
          <div className="flex items-center gap-2">
            {daysUntilAudit <= 7 && daysUntilAudit > 0 && (
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            )}
            {daysUntilAudit <= 0 && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            <span className={daysUntilAudit <= 7 ? 'text-yellow-600' : daysUntilAudit <= 0 ? 'text-red-600' : ''}>
              {formatDate(date)}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'complianceScore',
      header: 'Skor',
      cell: ({ row }) => {
        const score = row.getValue('complianceScore') as number;
        if (score === undefined || score === null) return '-';

        const getScoreColor = (score: number) => {
          if (score >= 90) return 'text-green-600';
          if (score >= 70) return 'text-yellow-600';
          return 'text-red-600';
        };

        return (
          <span className={`font-medium ${getScoreColor(score)}`}>
            {score}%
          </span>
        );
      },
    },
    {
      id: 'actions',
      header: 'Aksi',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          {onView && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onView(row.original)}
              title="Lihat Detail"
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}
          {onComplianceCheck && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onComplianceCheck(row.original)}
              title="Periksa Compliance"
            >
              <Shield className="h-4 w-4" />
            </Button>
          )}
          {onGenerateReport && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onGenerateReport(row.original)}
              title="Generate Laporan"
            >
              <FileText className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Gagal memuat data compliance</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="mt-2"
            >
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});

  const table = useReactTable({
    data: data?.data || [],
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
    },
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Status Compliance BPOM</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array.from({ length: query.limit || 10 }).map((_, index) => (
                  <TableRow key={index}>
                    {columns.map((_, colIndex) => (
                      <TableCell key={colIndex}>
                        <Skeleton className="h-4 w-full" />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className={onView ? 'cursor-pointer hover:bg-muted/50' : ''}
                    onClick={() => onView?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    Tidak ada data compliance.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
