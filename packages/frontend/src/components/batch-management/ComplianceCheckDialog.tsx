'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ProductSelector } from '@/components/ui/product-selector';
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Loader2,
  FileText,
  Clock
} from 'lucide-react';
import { useBPOMComplianceCheck } from '@/hooks/useBatchManagement';
import { formatDate } from '@/lib/utils';

const complianceCheckSchema = z.object({
  productId: z.string().min(1, 'Produk harus dipilih'),
  batchNumber: z.string().min(1, 'Batch number harus diisi'),
  userId: z.string().optional(),
});

type ComplianceCheckFormData = z.infer<typeof complianceCheckSchema>;

interface ComplianceCheckDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialData?: Partial<ComplianceCheckFormData>;
}

export function ComplianceCheckDialog({
  open,
  onOpenChange,
  initialData,
}: ComplianceCheckDialogProps) {
  const [checkResult, setCheckResult] = useState<any>(null);
  const complianceCheckMutation = useBPOMComplianceCheck();

  const form = useForm<ComplianceCheckFormData>({
    resolver: zodResolver(complianceCheckSchema),
    defaultValues: {
      productId: '',
      batchNumber: '',
      userId: '',
      ...initialData,
    },
  });

  const onSubmit = async (data: ComplianceCheckFormData) => {
    try {
      const result = await complianceCheckMutation.mutateAsync(data);
      setCheckResult(result);
    } catch (error) {
      console.error('Compliance check failed:', error);
    }
  };

  const handleClose = () => {
    form.reset();
    setCheckResult(null);
    onOpenChange(false);
  };

  const getComplianceStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      COMPLIANT: 'default',
      NON_COMPLIANT: 'destructive',
      PENDING_REVIEW: 'secondary',
      CONTROLLED: 'outline',
    };

    const icons: Record<string, any> = {
      COMPLIANT: <CheckCircle className="h-3 w-3" />,
      NON_COMPLIANT: <XCircle className="h-3 w-3" />,
      PENDING_REVIEW: <Clock className="h-3 w-3" />,
      CONTROLLED: <Shield className="h-3 w-3" />,
    };

    const labels: Record<string, string> = {
      COMPLIANT: 'Compliant',
      NON_COMPLIANT: 'Non-Compliant',
      PENDING_REVIEW: 'Pending Review',
      CONTROLLED: 'Controlled',
    };

    return (
      <Badge variant={variants[status] || 'secondary'} className="gap-1">
        {icons[status]}
        {labels[status] || status}
      </Badge>
    );
  };

  const getRiskLevelBadge = (level: string) => {
    const variants: Record<string, any> = {
      LOW: 'default',
      MEDIUM: 'secondary',
      HIGH: 'destructive',
      CRITICAL: 'destructive',
    };

    const labels: Record<string, string> = {
      LOW: 'Rendah',
      MEDIUM: 'Sedang',
      HIGH: 'Tinggi',
      CRITICAL: 'Kritis',
    };

    return (
      <Badge variant={variants[level] || 'secondary'}>
        {labels[level] || level}
      </Badge>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Pemeriksaan Compliance BPOM
          </DialogTitle>
          <DialogDescription>
            Lakukan pemeriksaan compliance BPOM untuk batch number dan produk
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form Section */}
          <div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="productId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Produk *</FormLabel>
                      <FormControl>
                        <ProductSelector
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Pilih produk..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="batchNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Batch Number *</FormLabel>
                      <FormControl>
                        <Input placeholder="Masukkan batch number..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button 
                  type="submit" 
                  className="w-full"
                  disabled={complianceCheckMutation.isPending}
                >
                  {complianceCheckMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Periksa Compliance
                </Button>
              </form>
            </Form>
          </div>

          {/* Results Section */}
          <div>
            {checkResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Hasil Pemeriksaan
                    {getComplianceStatusBadge(checkResult.status)}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Overall Score */}
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Skor Compliance:</span>
                    <div className="flex items-center gap-2">
                      <span className="text-2xl font-bold">
                        {checkResult.complianceScore || 0}%
                      </span>
                      {getRiskLevelBadge(checkResult.riskLevel || 'LOW')}
                    </div>
                  </div>

                  <Separator />

                  {/* Compliance Categories */}
                  {checkResult.categories && (
                    <div>
                      <h4 className="font-medium mb-2">Kategori Compliance:</h4>
                      <div className="space-y-2">
                        {checkResult.categories.map((category: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                            <span className="text-sm">{category.name}</span>
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">
                                {category.score}%
                              </span>
                              {category.score >= 90 ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : category.score >= 70 ? (
                                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                              ) : (
                                <XCircle className="h-4 w-4 text-red-500" />
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Issues */}
                  {checkResult.issues && checkResult.issues.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2 text-red-600">Masalah Ditemukan:</h4>
                      <ul className="space-y-1">
                        {checkResult.issues.map((issue: any, index: number) => (
                          <li key={index} className="text-sm flex items-start gap-2">
                            <XCircle className="h-3 w-3 mt-0.5 flex-shrink-0 text-red-500" />
                            <div>
                              <span className="font-medium">{issue.category}:</span> {issue.description}
                              {issue.severity && (
                                <Badge variant="outline" className="ml-2 text-xs">
                                  {issue.severity}
                                </Badge>
                              )}
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Recommendations */}
                  {checkResult.recommendations && checkResult.recommendations.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2 text-blue-600">Rekomendasi:</h4>
                      <ul className="space-y-1">
                        {checkResult.recommendations.map((rec: string, index: number) => (
                          <li key={index} className="text-sm flex items-start gap-2">
                            <FileText className="h-3 w-3 mt-0.5 flex-shrink-0 text-blue-500" />
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Next Audit */}
                  {checkResult.nextAuditDue && (
                    <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                      <span className="text-sm font-medium">Audit Berikutnya:</span>
                      <span className="text-sm">{formatDate(checkResult.nextAuditDue)}</span>
                    </div>
                  )}

                  {/* Controlled Substance Info */}
                  {checkResult.isControlledSubstance && (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                      <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-4 w-4 text-yellow-600" />
                        <span className="font-medium text-yellow-800">Zat Terkontrol</span>
                      </div>
                      <p className="text-sm text-yellow-700">
                        Produk ini termasuk zat terkontrol dan memerlukan penanganan khusus sesuai regulasi BPOM.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Tutup
          </Button>
          {checkResult && (
            <Button onClick={() => console.log('Generate report:', checkResult)}>
              <FileText className="mr-2 h-4 w-4" />
              Generate Laporan
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
