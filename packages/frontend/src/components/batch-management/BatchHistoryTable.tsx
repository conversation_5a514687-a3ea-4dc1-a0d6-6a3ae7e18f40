'use client';

import React from 'react';
import {
  ColumnDef,
  flexRender,
  useReactTable,
  getCoreRowModel,
  SortingState,
  VisibilityState
} from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { Eye, Edit, Trash2, AlertTriangle } from 'lucide-react';
import { BatchHistoryItem, BatchManagementQueryParams } from '@/types/batch-management';
import { useBatchHistory } from '@/hooks/useBatchManagement';
import { formatDate, formatCurrency } from '@/lib/utils';
import { BATCH_STATUS_OPTIONS, COMPLIANCE_STATUS_OPTIONS } from '@/lib/constants/batch-management';

interface BatchHistoryTableProps {
  query: BatchManagementQueryParams;
  onQueryChange: (query: BatchManagementQueryParams) => void;
  onView?: (batch: BatchHistoryItem) => void;
  onEdit?: (batch: BatchHistoryItem) => void;
  onDelete?: (batch: BatchHistoryItem) => void;
}

export function BatchHistoryTable({
  query,
  onQueryChange,
  onView,
  onEdit,
  onDelete,
}: BatchHistoryTableProps) {
  const { data, isLoading, error } = useBatchHistory(query);

  const getStatusBadge = (status: string) => {
    const option = BATCH_STATUS_OPTIONS.find(opt => opt.value === status);
    if (!option) return <Badge variant="secondary">{status}</Badge>;

    const variants: Record<string, any> = {
      ACTIVE: 'default',
      EXPIRED: 'destructive',
      EXPIRING_SOON: 'secondary',
      RECALLED: 'destructive',
      QUARANTINE: 'secondary',
      DISPOSED: 'outline',
    };

    return (
      <Badge variant={variants[status] || 'secondary'}>
        {option.label}
      </Badge>
    );
  };

  const getComplianceBadge = (status: string) => {
    const option = COMPLIANCE_STATUS_OPTIONS.find(opt => opt.value === status);
    if (!option) return <Badge variant="secondary">{status}</Badge>;

    const variants: Record<string, any> = {
      COMPLIANT: 'default',
      NON_COMPLIANT: 'destructive',
      PENDING_REVIEW: 'secondary',
      CONTROLLED: 'outline',
      IMPORTED: 'outline',
    };

    return (
      <Badge variant={variants[status] || 'secondary'}>
        {option.label}
      </Badge>
    );
  };

  const columns: ColumnDef<BatchHistoryItem>[] = [
    {
      accessorKey: 'batchNumber',
      header: 'Batch Number',
      cell: ({ row }) => (
        <div className="font-medium">
          {row.getValue('batchNumber')}
        </div>
      ),
    },
    {
      accessorKey: 'productName',
      header: 'Produk',
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate">
          {row.getValue('productName')}
        </div>
      ),
    },
    {
      accessorKey: 'supplierName',
      header: 'Supplier',
      cell: ({ row }) => (
        <div className="max-w-[150px] truncate">
          {row.getValue('supplierName') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => getStatusBadge(row.getValue('status')),
    },
    {
      accessorKey: 'complianceStatus',
      header: 'Compliance',
      cell: ({ row }) => getComplianceBadge(row.getValue('complianceStatus')),
    },
    {
      accessorKey: 'quantityOnHand',
      header: 'Stok',
      cell: ({ row }) => (
        <div className="text-right">
          {row.getValue('quantityOnHand')}
        </div>
      ),
    },
    {
      accessorKey: 'expiryDate',
      header: 'Tanggal Kedaluwarsa',
      cell: ({ row }) => {
        const date = row.getValue('expiryDate') as string;
        if (!date) return '-';

        const expiryDate = new Date(date);
        const now = new Date();
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

        return (
          <div className="flex items-center gap-2">
            {daysUntilExpiry <= 30 && daysUntilExpiry > 0 && (
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            )}
            {daysUntilExpiry <= 0 && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            <span className={daysUntilExpiry <= 30 ? 'text-yellow-600' : daysUntilExpiry <= 0 ? 'text-red-600' : ''}>
              {formatDate(date)}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'receivedDate',
      header: 'Tanggal Terima',
      cell: ({ row }) => formatDate(row.getValue('receivedDate')),
    },
    {
      id: 'actions',
      header: 'Aksi',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          {onView && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onView(row.original)}
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(row.original)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {onDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(row.original)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Gagal memuat riwayat batch</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="mt-2"
            >
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});

  const table = useReactTable({
    data: data?.data || [],
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
    },
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Riwayat Batch Number</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array.from({ length: query.limit || 10 }).map((_, index) => (
                  <TableRow key={index}>
                    {columns.map((_, colIndex) => (
                      <TableCell key={colIndex}>
                        <Skeleton className="h-4 w-full" />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className={onView ? 'cursor-pointer hover:bg-muted/50' : ''}
                    onClick={() => onView?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    Tidak ada data batch.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
