import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { batchManagementApi } from '@/lib/api/batch-management';
import {
  BatchManagementStats,
  BatchHistoryItem,
  BPOMComplianceData,
  BatchAuditLogItem,
  ExpiryTrackingItem,
  BatchValidationResult,
  BatchManagementQueryParams,
  ValidateBatchNumberDto,
  BPOMComplianceCheckDto,
  BatchExportParams,
  PaginatedResponse
} from '@/types/batch-management';

// Query Keys
export const batchManagementKeys = {
  all: ['batch-management'] as const,
  stats: () => [...batchManagementKeys.all, 'stats'] as const,
  history: () => [...batchManagementKeys.all, 'history'] as const,
  historyList: (params: BatchManagementQueryParams) => [...batchManagementKeys.history(), 'list', params] as const,
  compliance: () => [...batchManagementKeys.all, 'compliance'] as const,
  auditLogs: () => [...batchManagementKeys.all, 'audit-logs'] as const,
  expiryTracking: () => [...batchManagementKeys.all, 'expiry-tracking'] as const,
  validation: () => [...batchManagementKeys.all, 'validation'] as const,
};

// Helper function to handle file download
const downloadFile = (blob: Blob, filename: string) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

// Query Hooks
export function useBatchManagementStats(period?: string) {
  return useQuery({
    queryKey: [...batchManagementKeys.stats(), period],
    queryFn: () => batchManagementApi.getBatchManagementStats(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useBatchHistory(params: BatchManagementQueryParams) {
  return useQuery({
    queryKey: batchManagementKeys.historyList(params),
    queryFn: () => batchManagementApi.getBatchHistory(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData, // Keep previous data while loading
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useBatchHistoryByNumber(batchNumber: string, includeAudit = false, limit = 50) {
  return useQuery({
    queryKey: [...batchManagementKeys.history(), 'detail', batchNumber, includeAudit, limit],
    queryFn: () => batchManagementApi.getBatchHistoryByNumber(batchNumber, includeAudit, limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!batchNumber,
  });
}

export function useBPOMComplianceData() {
  return useQuery({
    queryKey: batchManagementKeys.compliance(),
    queryFn: () => batchManagementApi.getBPOMComplianceStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useBatchComplianceStatus(params: {
  productIds?: string[];
  batchNumbers?: string[];
  limit?: number;
  offset?: number;
}) {
  return useQuery({
    queryKey: [...batchManagementKeys.compliance(), 'status', params],
    queryFn: () => batchManagementApi.getBatchComplianceStatus(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!(params.productIds?.length || params.batchNumbers?.length),
  });
}

export function useControlledSubstances() {
  return useQuery({
    queryKey: [...batchManagementKeys.compliance(), 'controlled-substances'],
    queryFn: () => batchManagementApi.getControlledSubstances(),
    staleTime: 10 * 60 * 1000, // 10 minutes - this data changes less frequently
  });
}

export function useValidationRules() {
  return useQuery({
    queryKey: [...batchManagementKeys.validation(), 'rules'],
    queryFn: () => batchManagementApi.getValidationRules(),
    staleTime: 30 * 60 * 1000, // 30 minutes - rules change very infrequently
  });
}

export function useBatchAuditLogs(params: BatchManagementQueryParams) {
  return useQuery({
    queryKey: [...batchManagementKeys.auditLogs(), params],
    queryFn: () => batchManagementApi.getBatchAuditLogs(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    placeholderData: (previousData) => previousData,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useExpiryTracking(params: BatchManagementQueryParams) {
  return useQuery({
    queryKey: [...batchManagementKeys.expiryTracking(), params],
    queryFn: () => batchManagementApi.getExpiryTracking(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    placeholderData: (previousData) => previousData,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

// Invalidation hooks
export function useBatchManagementInvalidate() {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => queryClient.invalidateQueries({ queryKey: batchManagementKeys.all }),
    invalidateStats: () => queryClient.invalidateQueries({ queryKey: batchManagementKeys.stats() }),
    invalidateHistory: () => queryClient.invalidateQueries({ queryKey: batchManagementKeys.history() }),
    invalidateCompliance: () => queryClient.invalidateQueries({ queryKey: batchManagementKeys.compliance() }),
    invalidateAuditLogs: () => queryClient.invalidateQueries({ queryKey: batchManagementKeys.auditLogs() }),
    invalidateExpiryTracking: () => queryClient.invalidateQueries({ queryKey: batchManagementKeys.expiryTracking() }),
  };
}

// Mutation hooks
export function useValidateBatchNumber() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ValidateBatchNumberDto) => batchManagementApi.validateBatchNumber(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: batchManagementKeys.validation() });
      queryClient.invalidateQueries({ queryKey: batchManagementKeys.stats() });
      toast.success('Validasi batch berhasil dilakukan');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Gagal melakukan validasi batch');
    },
  });
}

export function useValidateBatchNumberRealTime() {
  return useMutation({
    mutationFn: ({ batchNumber, productId }: { batchNumber: string; productId: string }) =>
      batchManagementApi.validateBatchNumberRealTime(batchNumber, productId),
  });
}

export function useCheckBatchUniqueness() {
  return useMutation({
    mutationFn: ({ batchNumber, productId, supplierId }: {
      batchNumber: string;
      productId: string;
      supplierId?: string;
    }) => batchManagementApi.checkBatchUniqueness(batchNumber, productId, supplierId),
  });
}

export function useBPOMComplianceCheck() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BPOMComplianceCheckDto) => batchManagementApi.performBPOMComplianceCheck(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: batchManagementKeys.compliance() });
      queryClient.invalidateQueries({ queryKey: batchManagementKeys.stats() });
      toast.success('Pemeriksaan compliance BPOM berhasil dilakukan');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Gagal melakukan pemeriksaan compliance BPOM');
    },
  });
}

export function useGenerateInspectionPrep() {
  return useMutation({
    mutationFn: (params: {
      startDate: string;
      endDate: string;
      productIds?: string[];
      includeControlledSubstances?: boolean;
    }) => batchManagementApi.generateInspectionPrep(params),
    onSuccess: () => {
      toast.success('Laporan persiapan inspeksi berhasil dibuat');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Gagal membuat laporan persiapan inspeksi');
    },
  });
}

export function useExportBatchData() {
  return useMutation({
    mutationFn: async (params: BatchExportParams) => {
      const blob = await batchManagementApi.exportBatchData(params);
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `batch-data-${timestamp}.${params.format.toLowerCase()}`;
      downloadFile(blob, filename);
    },
    onSuccess: () => {
      toast.success('Data batch berhasil diekspor');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Gagal mengekspor data batch');
    },
  });
}
