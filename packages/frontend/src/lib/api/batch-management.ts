import { apiClient } from '../axios';
import {
  BatchManagementStats,
  BatchHistoryItem,
  BPOMComplianceData,
  BatchAuditLogItem,
  ExpiryTrackingItem,
  BatchValidationResult,
  BatchManagementQueryParams,
  ValidateBatchNumberDto,
  BPOMComplianceCheckDto,
  BatchExportParams,
  PaginatedResponse,
  BatchManagementResponse
} from '@/types/batch-management';

// Base API path for batch management
const BASE_PATH = '/api/procurement';

// Batch Management Statistics
export const batchManagementApi = {
  // Statistics
  async getBatchManagementStats(period?: string): Promise<BatchManagementStats> {
    const params = new URLSearchParams();
    if (period) params.set('period', period);

    const response = await apiClient.get<BatchManagementResponse<BatchManagementStats>>(
      `${BASE_PATH}/batch-validation/statistics?${params.toString()}`
    );
    return response.data.data;
  },

  // Batch History
  async getBatchHistory(params: BatchManagementQueryParams): Promise<PaginatedResponse<BatchHistoryItem>> {
    const searchParams = new URLSearchParams();

    // Add pagination parameters
    if (params.page) searchParams.set('page', params.page.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.sortBy) searchParams.set('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);

    // Add filter parameters
    if (params.search) searchParams.set('search', params.search);
    if (params.status) searchParams.set('status', params.status);
    if (params.complianceStatus) searchParams.set('complianceStatus', params.complianceStatus);
    if (params.productId) searchParams.set('productId', params.productId);
    if (params.supplierId) searchParams.set('supplierId', params.supplierId);
    if (params.batchNumber) searchParams.set('batchNumber', params.batchNumber);
    if (params.expiryDateFrom) searchParams.set('expiryDateFrom', params.expiryDateFrom);
    if (params.expiryDateTo) searchParams.set('expiryDateTo', params.expiryDateTo);

    const response = await apiClient.get<BatchManagementResponse<PaginatedResponse<BatchHistoryItem>>>(
      `${BASE_PATH}/batch-validation/history?${searchParams.toString()}`
    );
    return response.data.data;
  },

  // Get specific batch history
  async getBatchHistoryByNumber(batchNumber: string, includeAudit = false, limit = 50): Promise<any> {
    const params = new URLSearchParams();
    if (includeAudit) params.set('includeAudit', 'true');
    params.set('limit', limit.toString());

    const response = await apiClient.get<BatchManagementResponse<any>>(
      `${BASE_PATH}/batch-validation/history/${encodeURIComponent(batchNumber)}?${params.toString()}`
    );
    return response.data.data;
  },

  // Batch Validation
  async validateBatchNumber(data: ValidateBatchNumberDto): Promise<BatchValidationResult> {
    const response = await apiClient.post<BatchManagementResponse<BatchValidationResult>>(
      `${BASE_PATH}/batch-validation/validate`,
      data
    );
    return response.data.data;
  },

  // Real-time validation
  async validateBatchNumberRealTime(batchNumber: string, productId: string): Promise<{
    isValid: boolean;
    message?: string;
    level: 'error' | 'warning' | 'success';
  }> {
    const response = await apiClient.post<BatchManagementResponse<any>>(
      `${BASE_PATH}/batch-validation/real-time`,
      { batchNumber, productId }
    );
    return response.data.data;
  },

  // Uniqueness check
  async checkBatchUniqueness(batchNumber: string, productId: string, supplierId?: string): Promise<{
    isUnique: boolean;
    conflictingBatches?: any[];
  }> {
    const response = await apiClient.post<BatchManagementResponse<any>>(
      `${BASE_PATH}/batch-validation/check-uniqueness`,
      { batchNumber, productId, supplierId }
    );
    return response.data.data;
  },

  // Get validation rules
  async getValidationRules(): Promise<any> {
    const response = await apiClient.get<BatchManagementResponse<any>>(
      `${BASE_PATH}/batch-validation/rules`
    );
    return response.data.data;
  },

  // BPOM Compliance
  async performBPOMComplianceCheck(data: BPOMComplianceCheckDto): Promise<any> {
    const response = await apiClient.post<BatchManagementResponse<any>>(
      `${BASE_PATH}/bpom-compliance/check`,
      data
    );
    return response.data.data;
  },

  // Get batch compliance status
  async getBatchComplianceStatus(params: {
    productIds?: string[];
    batchNumbers?: string[];
    limit?: number;
    offset?: number;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params.productIds?.length) searchParams.set('productIds', params.productIds.join(','));
    if (params.batchNumbers?.length) searchParams.set('batchNumbers', params.batchNumbers.join(','));
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());

    const response = await apiClient.get<BatchManagementResponse<any>>(
      `${BASE_PATH}/bpom-compliance/batch-status?${searchParams.toString()}`
    );
    return response.data.data;
  },

  // Get controlled substances data
  async getControlledSubstances(): Promise<any> {
    const response = await apiClient.get<BatchManagementResponse<any>>(
      `${BASE_PATH}/bpom-compliance/controlled-substances`
    );
    return response.data.data;
  },

  // Generate inspection preparation report
  async generateInspectionPrep(params: {
    startDate: string;
    endDate: string;
    productIds?: string[];
    includeControlledSubstances?: boolean;
  }): Promise<any> {
    const response = await apiClient.post<BatchManagementResponse<any>>(
      `${BASE_PATH}/bpom-compliance/inspection-prep`,
      params
    );
    return response.data.data;
  },

  // Get BPOM compliance statistics
  async getBPOMComplianceStats(): Promise<BPOMComplianceData> {
    const response = await apiClient.get<BatchManagementResponse<BPOMComplianceData>>(
      `${BASE_PATH}/bpom-compliance/statistics`
    );
    return response.data.data;
  },

  // Batch Audit Logs
  async getBatchAuditLogs(params: BatchManagementQueryParams): Promise<PaginatedResponse<BatchAuditLogItem>> {
    const searchParams = new URLSearchParams();

    // Add pagination parameters
    if (params.page) searchParams.set('page', params.page.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.sortBy) searchParams.set('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);

    // Add filter parameters
    if (params.batchNumber) searchParams.set('batchNumber', params.batchNumber);
    if (params.productId) searchParams.set('productId', params.productId);
    if (params.supplierId) searchParams.set('supplierId', params.supplierId);

    const response = await apiClient.get<BatchManagementResponse<PaginatedResponse<BatchAuditLogItem>>>(
      `${BASE_PATH}/batch-validation/audit-logs?${searchParams.toString()}`
    );
    return response.data.data;
  },

  // Expiry Tracking
  async getExpiryTracking(params: BatchManagementQueryParams): Promise<PaginatedResponse<ExpiryTrackingItem>> {
    const searchParams = new URLSearchParams();

    // Add pagination parameters
    if (params.page) searchParams.set('page', params.page.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.sortBy) searchParams.set('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);

    // Add filter parameters for expiry tracking
    if (params.expiryDateFrom) searchParams.set('expiryDateFrom', params.expiryDateFrom);
    if (params.expiryDateTo) searchParams.set('expiryDateTo', params.expiryDateTo);
    if (params.status) searchParams.set('status', params.status);

    const response = await apiClient.get<BatchManagementResponse<PaginatedResponse<ExpiryTrackingItem>>>(
      `${BASE_PATH}/batch-validation/expiry-tracking?${searchParams.toString()}`
    );
    return response.data.data;
  },

  // Export functionality
  async exportBatchData(params: BatchExportParams): Promise<Blob> {
    const searchParams = new URLSearchParams();
    searchParams.set('format', params.format);
    if (params.includeAuditTrail) searchParams.set('includeAuditTrail', 'true');
    if (params.includeComplianceData) searchParams.set('includeComplianceData', 'true');

    // Add filter parameters if provided
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.set(key, value.toString());
        }
      });
    }

    const response = await apiClient.get(
      `${BASE_PATH}/batch-validation/export?${searchParams.toString()}`,
      { responseType: 'blob' }
    );
    return response.data;
  },
};
