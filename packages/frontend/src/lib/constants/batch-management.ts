import { BatchManagementQueryParams } from '@/types/batch-management';

// Default pagination settings for batch management
export const DEFAULT_BATCH_MANAGEMENT_PAGINATION = {
  page: 1,
  limit: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc' as const,
};

// Batch status options for filters
export const BATCH_STATUS_OPTIONS = [
  { value: 'ACTIVE', label: 'Aktif' },
  { value: 'EXPIRED', label: 'Kedaluwar<PERSON>' },
  { value: 'EXPIRING_SOON', label: '<PERSON><PERSON>' },
  { value: 'RECALLED', label: 'Ditarik' },
  { value: 'QUARANTINE', label: 'Karantina' },
  { value: 'DISPOSED', label: 'Dimusnahkan' },
];

// Compliance status options for filters
export const COMPLIANCE_STATUS_OPTIONS = [
  { value: 'COMPLIANT', label: 'BPOM Compliant' },
  { value: 'NON_COMPLIANT', label: 'Non-Compliant' },
  { value: 'PENDING_REVIEW', label: 'Menunggu Review' },
  { value: 'CONTROLLED', label: 'Obat Terkontrol' },
  { value: 'IMPORTED', label: 'Produk Impor' },
];

// Validation level options
export const VALIDATION_LEVEL_OPTIONS = [
  { value: 'BASIC', label: 'Dasar' },
  { value: 'STANDARD', label: 'Standar' },
  { value: 'BPOM_COMPLIANT', label: 'BPOM Compliant' },
  { value: 'CONTROLLED', label: 'Terkontrol' },
];

// Audit action labels
export const BATCH_AUDIT_ACTION_LABELS = {
  CREATED: 'Dibuat',
  UPDATED: 'Diperbarui',
  VALIDATED: 'Divalidasi',
  REJECTED: 'Ditolak',
  SUBSTITUTED: 'Diganti',
  EXPIRED: 'Kedaluwarsa',
  RECALLED: 'Ditarik',
  COMPLIANCE_CHECK: 'Pemeriksaan Compliance',
  FORMAT_VALIDATION: 'Validasi Format',
  UNIQUENESS_CHECK: 'Pemeriksaan Keunikan',
  GOODS_RECEIPT: 'Penerimaan Barang',
  INVENTORY_CREATED: 'Inventory Dibuat',
  STOCK_MOVEMENT: 'Pergerakan Stok',
};

// Export format options
export const EXPORT_FORMAT_OPTIONS = [
  { value: 'CSV', label: 'CSV' },
  { value: 'EXCEL', label: 'Excel' },
  { value: 'PDF', label: 'PDF' },
];

// Date range options for statistics
export const STATS_PERIOD_OPTIONS = [
  { value: '7', label: '7 Hari Terakhir' },
  { value: '30', label: '30 Hari Terakhir' },
  { value: '90', label: '90 Hari Terakhir' },
  { value: '365', label: '1 Tahun Terakhir' },
];

// Expiry warning thresholds (in days)
export const EXPIRY_WARNING_THRESHOLDS = {
  CRITICAL: 30, // Less than 30 days
  WARNING: 90,  // Less than 90 days
  NORMAL: 180,  // Less than 180 days
};

// Batch number format patterns
export const BATCH_FORMAT_PATTERNS = {
  STANDARD: {
    pattern: '^[A-Z]{2,4}[0-9]{4,8}[A-Z0-9]{0,8}$',
    description: 'Format standar farmasi (2-4 huruf + 4-8 angka + opsional alphanumeric)',
    example: 'ABC12345678'
  },
  BPOM_COMPLIANT: {
    pattern: '^[A-Z]{2,4}[0-9]{6}[A-Z0-9]{2,6}$',
    description: 'Format sesuai BPOM dengan kode tanggal (2-4 huruf + 6 angka tanggal + 2-6 alphanumeric)',
    example: 'KMF240615AB'
  },
  CONTROLLED: {
    pattern: '^[A-Z]{3}[0-9]{8}[A-Z]{2}$',
    description: 'Format khusus obat terkontrol (3 huruf + 8 angka + 2 huruf)',
    example: 'NAR12345678AB'
  },
};

// Default query parameters
export const DEFAULT_BATCH_MANAGEMENT_QUERY: BatchManagementQueryParams = {
  ...DEFAULT_BATCH_MANAGEMENT_PAGINATION,
};

// Table column options for sorting
export const SORTABLE_COLUMNS = [
  { value: 'batchNumber', label: 'Nomor Batch' },
  { value: 'productName', label: 'Nama Produk' },
  { value: 'supplierName', label: 'Supplier' },
  { value: 'expiryDate', label: 'Tanggal Kedaluwarsa' },
  { value: 'receivedDate', label: 'Tanggal Diterima' },
  { value: 'createdAt', label: 'Tanggal Dibuat' },
  { value: 'quantityOnHand', label: 'Stok Tersedia' },
];

// Compliance category colors
export const COMPLIANCE_LEVEL_COLORS = {
  high: 'text-green-600 bg-green-50 border-green-200',
  medium: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  low: 'text-red-600 bg-red-50 border-red-200',
};

// Severity colors for issues
export const SEVERITY_COLORS = {
  high: 'text-red-500',
  medium: 'text-yellow-500',
  low: 'text-blue-500',
};

// Badge variants for severity
export const SEVERITY_BADGE_VARIANTS = {
  high: 'destructive',
  medium: 'secondary',
  low: 'default',
};
