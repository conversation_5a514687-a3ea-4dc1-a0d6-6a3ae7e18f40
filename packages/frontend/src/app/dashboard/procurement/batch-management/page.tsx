import { Metada<PERSON> } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { authOptions } from "@/lib/auth";
import { SiteHeader } from "@/components/site-header";
import { BatchManagementPageClient } from "@/components/batch-management/BatchManagementPageClient";
import { BatchManagementStatsCards } from "@/components/batch-management/batch-management-stats-cards";
import { BatchManagementQueryParams } from "@/types/batch-management";
import { DEFAULT_BATCH_MANAGEMENT_PAGINATION } from "@/lib/constants/batch-management";

export const metadata: Metadata = {
  title: "Manajemen Batch Number - Apotek App",
  description:
    "Dashboard untuk mengelola batch number, validasi, dan compliance BPOM",
};

interface BatchManagementPageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

export default async function BatchManagementPage({
  searchParams,
}: BatchManagementPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/login");
  }

  // Parse search parameters
  const resolvedSearchParams = await searchParams;

  const filters: BatchManagementQueryParams = {
    page: resolvedSearchParams.page
      ? parseInt(resolvedSearchParams.page as string)
      : DEFAULT_BATCH_MANAGEMENT_PAGINATION.page,
    limit: resolvedSearchParams.limit
      ? parseInt(resolvedSearchParams.limit as string)
      : DEFAULT_BATCH_MANAGEMENT_PAGINATION.limit,
    search: resolvedSearchParams.search as string,
    status: resolvedSearchParams.status as any,
    complianceStatus: resolvedSearchParams.complianceStatus as any,
    productId: resolvedSearchParams.productId as string,
    supplierId: resolvedSearchParams.supplierId as string,
    expiryDateFrom: resolvedSearchParams.expiryDateFrom as string,
    expiryDateTo: resolvedSearchParams.expiryDateTo as string,
    batchNumber: resolvedSearchParams.batchNumber as string,
    sortBy:
      (resolvedSearchParams.sortBy as string) ||
      DEFAULT_BATCH_MANAGEMENT_PAGINATION.sortBy,
    sortOrder:
      (resolvedSearchParams.sortOrder as "asc" | "desc") ||
      DEFAULT_BATCH_MANAGEMENT_PAGINATION.sortOrder,
  };

  // Extract period for statistics
  const statsPeriod = (resolvedSearchParams.period as string) || undefined;

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Stats Cards */}
          <div className="px-4 lg:px-6">
            <BatchManagementStatsCards period={statsPeriod} />
          </div>

          {/* Client-side interactive components */}
          <div className="px-4 lg:px-6">
            <BatchManagementPageClient
              initialQuery={filters}
            />
          </div>
        </div>
      </div>
    </>
  );
}
