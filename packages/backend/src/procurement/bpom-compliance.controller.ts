import { Controller, Get, Post, Body, Param, Query, BadRequestException, UseGuards, ValidationPipe, HttpCode, HttpStatus } from '@nestjs/common';
import { IsString, IsOptional, IsArray, IsBoolean } from 'class-validator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ManagerGuard } from '../suppliers/guards/manager.guard';
import { BPOMComplianceService, BPOMComplianceResult } from './services/bpom-compliance.service';
import { PrismaService } from '../prisma/prisma.service';

/**
 * DTO for BPOM compliance check request
 */
export class BPOMComplianceCheckDto {
  @IsString()
  productId: string;

  @IsString()
  batchNumber: string;

  @IsOptional()
  @IsString()
  userId?: string;
}

/**
 * DTO for controlled substance tracking request
 */
export class ControlledSubstanceTrackingDto {
  productId: string;
  batchNumber: string;
  action: 'RECEIVED' | 'DISPENSED' | 'TRANSFERRED' | 'DESTROYED';
  quantity: number;
  notes?: string;
  userId?: string;
}

/**
 * DTO for BPOM inspection preparation request
 */
export class BPOMInspectionPrepDto {
  @IsString()
  startDate: string;

  @IsString()
  endDate: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  productIds?: string[];

  @IsOptional()
  @IsBoolean()
  includeControlledSubstances?: boolean;
}

/**
 * Controller for BPOM compliance management
 * Handles Indonesian pharmaceutical regulatory compliance
 */
@Controller('procurement/bpom-compliance')
@UseGuards(JwtAuthGuard)
export class BPOMComplianceController {
  constructor(
    private readonly bpomComplianceService: BPOMComplianceService,
    private readonly prisma: PrismaService
  ) { }

  /**
   * Perform BPOM compliance check for a batch
   * POST /api/procurement/bpom-compliance/check
   */
  @Post('check')
  @HttpCode(HttpStatus.OK)
  @UseGuards(ManagerGuard) // Admin or Pharmacist can perform compliance checks
  async performComplianceCheck(@Body(ValidationPipe) dto: BPOMComplianceCheckDto) {
    try {
      if (!dto.productId || !dto.batchNumber) {
        throw new BadRequestException('Product ID dan batch number wajib diisi');
      }

      const result = await this.bpomComplianceService.performComplianceCheck(
        dto.productId,
        dto.batchNumber,
        dto.userId
      );

      return {
        success: true,
        data: result,
        message: 'Pemeriksaan compliance BPOM berhasil dilakukan'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal melakukan pemeriksaan compliance BPOM',
        error: error.message
      });
    }
  }

  /**
   * Get BPOM compliance status for multiple batches
   * GET /api/procurement/bpom-compliance/batch-status
   */
  @Get('batch-status')
  async getBatchComplianceStatus(
    @Query('productIds') productIds?: string,
    @Query('batchNumbers') batchNumbers?: string,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string
  ) {
    try {
      const productIdArray = productIds ? productIds.split(',') : [];
      const batchNumberArray = batchNumbers ? batchNumbers.split(',') : [];
      const limitNum = limit ? parseInt(limit) : 50;
      const offsetNum = offset ? parseInt(offset) : 0;

      // Get inventory items with batch numbers
      const where: any = {};
      if (productIdArray.length > 0) {
        where.productId = { in: productIdArray };
      }
      if (batchNumberArray.length > 0) {
        where.batchNumber = { in: batchNumberArray };
      }

      const inventoryItems = await this.prisma.inventoryItem.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              type: true,
              medicineClassification: true,
              bpomNumber: true,
            }
          }
        },
        take: limitNum,
        skip: offsetNum,
      });

      // Perform compliance checks for each batch
      const complianceResults = await Promise.all(
        inventoryItems.map(async (item) => {
          if (!item.batchNumber) return null;

          const compliance = await this.bpomComplianceService.performComplianceCheck(
            item.productId,
            item.batchNumber
          );

          return {
            productId: item.productId,
            productName: item.product.name,
            batchNumber: item.batchNumber,
            compliance,
          };
        })
      );

      const validResults = complianceResults.filter(result => result !== null);

      return {
        success: true,
        data: {
          results: validResults,
          total: validResults.length,
          summary: {
            compliant: validResults.filter(r => r.compliance.isCompliant).length,
            nonCompliant: validResults.filter(r => !r.compliance.isCompliant).length,
            controlled: validResults.filter(r => r.compliance.controlledSubstance).length,
            imported: validResults.filter(r => r.compliance.importProduct).length,
          }
        },
        message: 'Status compliance batch berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil status compliance batch',
        error: error.message
      });
    }
  }

  /**
   * Get controlled substance tracking data
   * GET /api/procurement/bpom-compliance/controlled-substances
   */
  @Get('controlled-substances')
  async getControlledSubstances(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('classification') classification?: string
  ) {
    try {
      const where: any = {
        product: {
          medicineClassification: {
            in: ['NARKOTIKA', 'PSIKOTROPIKA']
          }
        },
        isActive: true
      };

      if (classification) {
        where.product.medicineClassification = classification;
      }

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = new Date(startDate);
        if (endDate) where.createdAt.lte = new Date(endDate);
      }

      const controlledItems = await this.prisma.inventoryItem.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              medicineClassification: true,
              bpomNumber: true,
            }
          },
          supplier: {
            select: {
              id: true,
              name: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      const trackingData = controlledItems.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product.name,
        batchNumber: item.batchNumber,
        classification: item.product.medicineClassification,
        quantity: item.quantityOnHand,
        unit: 'units', // Would need to join with ProductUnit for actual unit name
        supplierName: item.supplier?.name,
        receivedDate: item.receivedDate,
        expiryDate: item.expiryDate,
        createdAt: item.createdAt,
        bpomNumber: item.product.bpomNumber,
      }));

      return {
        success: true,
        data: {
          items: trackingData,
          summary: {
            totalItems: trackingData.length,
            narkotika: trackingData.filter(item => item.classification === 'NARKOTIKA').length,
            psikotropika: trackingData.filter(item => item.classification === 'PSIKOTROPIKA').length,
            totalQuantity: trackingData.reduce((sum, item) => sum + (item.quantity || 0), 0),
          }
        },
        message: 'Data obat terkontrol berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil data obat terkontrol',
        error: error.message
      });
    }
  }

  /**
   * Generate BPOM inspection preparation report
   * POST /api/procurement/bpom-compliance/inspection-prep
   */
  @Post('inspection-prep')
  @HttpCode(HttpStatus.OK)
  @UseGuards(ManagerGuard) // Admin or Pharmacist can generate inspection reports
  async generateInspectionPrep(@Body(ValidationPipe) dto: BPOMInspectionPrepDto) {
    try {
      if (!dto.startDate || !dto.endDate) {
        throw new BadRequestException('Start date dan end date wajib diisi');
      }

      const startDate = new Date(dto.startDate);
      const endDate = new Date(dto.endDate);

      const where: any = {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        isActive: true
      };

      if (dto.productIds && dto.productIds.length > 0) {
        where.productId = { in: dto.productIds };
      }

      if (dto.includeControlledSubstances) {
        where.product = {
          medicineClassification: {
            in: ['NARKOTIKA', 'PSIKOTROPIKA']
          }
        };
      }

      const inventoryItems = await this.prisma.inventoryItem.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              type: true,
              medicineClassification: true,
              bpomNumber: true,
            }
          },
          supplier: {
            select: {
              id: true,
              name: true,
            }
          }
        }
      });

      // Perform compliance checks for all items
      const complianceChecks = await Promise.all(
        inventoryItems.map(async (item) => {
          if (!item.batchNumber) return null;

          const compliance = await this.bpomComplianceService.performComplianceCheck(
            item.productId,
            item.batchNumber
          );

          return {
            item,
            compliance
          };
        })
      );

      const validChecks = complianceChecks.filter(check => check !== null);

      // Prepare inspection data
      const inspectionData = {
        period: {
          startDate: dto.startDate,
          endDate: dto.endDate
        },
        summary: {
          totalBatches: validChecks.length,
          compliantBatches: validChecks.filter(c => c.compliance.isCompliant).length,
          nonCompliantBatches: validChecks.filter(c => !c.compliance.isCompliant).length,
          controlledSubstances: validChecks.filter(c => c.compliance.controlledSubstance).length,
          importedProducts: validChecks.filter(c => c.compliance.importProduct).length,
        },
        complianceIssues: validChecks
          .filter(c => !c.compliance.isCompliant)
          .map(c => ({
            productName: c.item.product.name,
            batchNumber: c.item.batchNumber,
            issues: c.compliance.errors.concat(c.compliance.warnings),
            complianceLevel: c.compliance.complianceLevel,
          })),
        controlledSubstanceReport: validChecks
          .filter(c => c.compliance.controlledSubstance)
          .map(c => ({
            productName: c.item.product.name,
            batchNumber: c.item.batchNumber,
            classification: c.item.product.medicineClassification,
            quantity: c.item.quantityOnHand,
            unit: 'units', // Would need to join with ProductUnit for actual unit name
            supplierName: c.item.supplier?.name,
            complianceStatus: c.compliance.isCompliant ? 'COMPLIANT' : 'NON_COMPLIANT',
          })),
        preparationChecklist: [
          {
            item: 'Dokumen registrasi BPOM lengkap',
            status: validChecks.every(c => c.compliance.registrationValid) ? 'COMPLETE' : 'NEEDS_ATTENTION'
          },
          {
            item: 'Batch records dan dokumentasi produksi',
            status: 'PENDING',
            notes: 'Perlu verifikasi manual'
          },
          {
            item: 'Sertifikat analisis (CoA) untuk semua batch',
            status: 'PENDING',
            notes: 'Perlu verifikasi manual'
          },
          {
            item: 'Dokumentasi penyimpanan obat terkontrol',
            status: validChecks.some(c => c.compliance.controlledSubstance) ? 'NEEDS_ATTENTION' : 'COMPLETE'
          },
          {
            item: 'Laporan adverse event (jika ada)',
            status: 'PENDING',
            notes: 'Perlu verifikasi manual'
          }
        ]
      };

      return {
        success: true,
        data: inspectionData,
        message: 'Laporan persiapan inspeksi BPOM berhasil dibuat'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal membuat laporan persiapan inspeksi',
        error: error.message
      });
    }
  }

  /**
   * Get BPOM compliance statistics
   * GET /api/procurement/bpom-compliance/statistics
   */
  @Get('statistics')
  async getComplianceStatistics(
    @Query('period') period?: string // '7d', '30d', '90d', '1y'
  ) {
    try {
      const periodDays = this.getPeriodDays(period || '30d');
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - periodDays);

      const inventoryItems = await this.prisma.inventoryItem.findMany({
        where: {
          createdAt: { gte: startDate },
          isActive: true,
          batchNumber: { not: null }
        },
        include: {
          product: {
            select: {
              type: true,
              medicineClassification: true,
              bpomNumber: true,
            }
          }
        }
      });

      const statistics = {
        totalBatches: inventoryItems.length,
        bpomRegistered: inventoryItems.filter(item => item.product.bpomNumber).length,
        controlledSubstances: inventoryItems.filter(item =>
          item.product.medicineClassification === 'NARKOTIKA' ||
          item.product.medicineClassification === 'PSIKOTROPIKA'
        ).length,
        medicines: inventoryItems.filter(item => item.product.type === 'MEDICINE').length,
        medicalDevices: inventoryItems.filter(item => item.product.type === 'MEDICAL_DEVICE').length,
        supplements: inventoryItems.filter(item => item.product.type === 'SUPPLEMENT').length,
        complianceRate: 0, // Will be calculated after compliance checks
        period: {
          days: periodDays,
          startDate: startDate.toISOString(),
          endDate: new Date().toISOString()
        }
      };

      // Calculate compliance rate (sample check for performance)
      const sampleSize = Math.min(100, inventoryItems.length);
      const sampleItems = inventoryItems.slice(0, sampleSize);

      let compliantCount = 0;
      for (const item of sampleItems) {
        if (item.batchNumber) {
          const compliance = await this.bpomComplianceService.performComplianceCheck(
            item.productId,
            item.batchNumber
          );
          if (compliance.isCompliant) compliantCount++;
        }
      }

      statistics.complianceRate = sampleSize > 0 ? (compliantCount / sampleSize) * 100 : 0;

      return {
        success: true,
        data: statistics,
        message: 'Statistik compliance BPOM berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil statistik compliance',
        error: error.message
      });
    }
  }

  /**
   * Helper method to convert period string to days
   */
  private getPeriodDays(period: string): number {
    switch (period) {
      case '7d': return 7;
      case '30d': return 30;
      case '90d': return 90;
      case '1y': return 365;
      default: return 30;
    }
  }
}
